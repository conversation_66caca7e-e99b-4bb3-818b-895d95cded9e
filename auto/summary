
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


echo
echo "Configuration summary"


if [ $USE_THREADS = YES ]; then
    echo "  + using threads"
fi

if [ $USE_PCRE = DISABLED ]; then
    echo "  + PCRE library is disabled"

else
    case $PCRE in
        YES)   echo "  + using system $PCRE_LIBRARY library" ;;
        NONE)  echo "  + PCRE library is not used" ;;
        *)     echo "  + using $PCRE_LIBRARY library: $PCRE" ;;
    esac
fi

case $OPENSSL in
    YES)   echo "  + using system OpenSSL library" ;;
    NONE)  echo "  + OpenSSL library is not used" ;;
    *)     echo "  + using OpenSSL library: $OPENSSL" ;;
esac

case $ZLIB in
    YES)   echo "  + using system zlib library" ;;
    NONE)  echo "  + zlib library is not used" ;;
    *)     echo "  + using zlib library: $ZLIB" ;;
esac

case $NGX_LIBATOMIC in
    YES)   echo "  + using system libatomic_ops library" ;;
    NO)    ;; # not used
    *)     echo "  + using libatomic_ops library: $NGX_LIBATOMIC" ;;
esac

echo


cat << END
  nginx path prefix: "$NGX_PREFIX"
  nginx binary file: "$NGX_SBIN_PATH"
  nginx modules path: "$NGX_MODULES_PATH"
  nginx configuration prefix: "$NGX_CONF_PREFIX"
  nginx configuration file: "$NGX_CONF_PATH"
  nginx pid file: "$NGX_PID_PATH"
END

if test -n "$NGX_ERROR_LOG_PATH"; then
    echo "  nginx error log file: \"$NGX_ERROR_LOG_PATH\""
else
    echo "  nginx logs errors to stderr"
fi

cat << END
  nginx http access log file: "$NGX_HTTP_LOG_PATH"
  nginx http client request body temporary files: "$NGX_HTTP_CLIENT_TEMP_PATH"
END

if [ $HTTP_PROXY = YES ]; then
    echo "  nginx http proxy temporary files: \"$NGX_HTTP_PROXY_TEMP_PATH\""
fi

if [ $HTTP_FASTCGI = YES ]; then
    echo "  nginx http fastcgi temporary files: \"$NGX_HTTP_FASTCGI_TEMP_PATH\""
fi

if [ $HTTP_UWSGI = YES ]; then
    echo "  nginx http uwsgi temporary files: \"$NGX_HTTP_UWSGI_TEMP_PATH\""
fi

if [ $HTTP_SCGI = YES ]; then
    echo "  nginx http scgi temporary files: \"$NGX_HTTP_SCGI_TEMP_PATH\""
fi

echo "$NGX_POST_CONF_MSG"
