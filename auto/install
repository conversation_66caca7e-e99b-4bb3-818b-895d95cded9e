
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


if [ $USE_PERL != NO ]; then

    cat << END                                                >> $NGX_MAKEFILE

install_perl_modules:
	cd $NGX_OBJS/src/http/modules/perl && \$(MAKE) install
END

    NGX_INSTALL_PERL_MODULES=install_perl_modules

fi


case ".$NGX_SBIN_PATH" in
    ./*)
    ;;

    *)
        NGX_SBIN_PATH=$NGX_PREFIX/$NGX_SBIN_PATH
    ;;
esac


case ".$NGX_MODULES_PATH" in
    ./*)
    ;;

    *)
        NGX_MODULES_PATH=$NGX_PREFIX/$NGX_MODULES_PATH
    ;;
esac

NGX_MODULES_PATH=`dirname $NGX_MODULES_PATH/.`


case ".$NGX_CONF_PATH" in
    ./*)
    ;;

    *)
        NGX_CONF_PATH=$NGX_PREFIX/$NGX_CONF_PATH
    ;;
esac


NGX_CONF_PREFIX=`dirname $NGX_CONF_PATH`


case ".$NGX_PID_PATH" in
    ./*)
    ;;

    *)
        NGX_PID_PATH=$NGX_PREFIX/$NGX_PID_PATH
    ;;
esac


case ".$NGX_ERROR_LOG_PATH" in
    ./* | .)
    ;;

    *)
        NGX_ERROR_LOG_PATH=$NGX_PREFIX/$NGX_ERROR_LOG_PATH
    ;;
esac


case ".$NGX_HTTP_LOG_PATH" in
    ./*)
    ;;

    *)
        NGX_HTTP_LOG_PATH=$NGX_PREFIX/$NGX_HTTP_LOG_PATH
    ;;
esac


if test -f man/nginx.8 ; then
    NGX_MAN=man/nginx.8
else
    NGX_MAN=docs/man/nginx.8
fi

if test -d html ; then
    NGX_HTML=html
else
    NGX_HTML=docs/html
fi

cat << END                                                    >> $NGX_MAKEFILE

manpage:	$NGX_OBJS/nginx.8

$NGX_OBJS/nginx.8:	$NGX_MAN $NGX_AUTO_CONFIG_H
	sed -e "s|%%PREFIX%%|$NGX_PREFIX|" \\
		-e "s|%%PID_PATH%%|$NGX_PID_PATH|" \\
		-e "s|%%CONF_PATH%%|$NGX_CONF_PATH|" \\
		-e "s|%%ERROR_LOG_PATH%%|${NGX_ERROR_LOG_PATH:-stderr}|" \\
		< $NGX_MAN > \$@

install:	build $NGX_INSTALL_PERL_MODULES
	test -d '\$(DESTDIR)$NGX_PREFIX' || mkdir -p '\$(DESTDIR)$NGX_PREFIX'

	test -d '\$(DESTDIR)`dirname "$NGX_SBIN_PATH"`' \\
		|| mkdir -p '\$(DESTDIR)`dirname "$NGX_SBIN_PATH"`'
	test ! -f '\$(DESTDIR)$NGX_SBIN_PATH' \\
		|| mv '\$(DESTDIR)$NGX_SBIN_PATH' \\
			'\$(DESTDIR)$NGX_SBIN_PATH.old'
	cp $NGX_OBJS/nginx$ngx_binext '\$(DESTDIR)$NGX_SBIN_PATH'

	test -d '\$(DESTDIR)$NGX_CONF_PREFIX' \\
		|| mkdir -p '\$(DESTDIR)$NGX_CONF_PREFIX'

	cp conf/koi-win '\$(DESTDIR)$NGX_CONF_PREFIX'
	cp conf/koi-utf '\$(DESTDIR)$NGX_CONF_PREFIX'
	cp conf/win-utf '\$(DESTDIR)$NGX_CONF_PREFIX'

	test -f '\$(DESTDIR)$NGX_CONF_PREFIX/mime.types' \\
		|| cp conf/mime.types '\$(DESTDIR)$NGX_CONF_PREFIX'
	cp conf/mime.types '\$(DESTDIR)$NGX_CONF_PREFIX/mime.types.default'

	test -f '\$(DESTDIR)$NGX_CONF_PREFIX/fastcgi_params' \\
		|| cp conf/fastcgi_params '\$(DESTDIR)$NGX_CONF_PREFIX'
	cp conf/fastcgi_params \\
		'\$(DESTDIR)$NGX_CONF_PREFIX/fastcgi_params.default'

	test -f '\$(DESTDIR)$NGX_CONF_PREFIX/fastcgi.conf' \\
		|| cp conf/fastcgi.conf '\$(DESTDIR)$NGX_CONF_PREFIX'
	cp conf/fastcgi.conf '\$(DESTDIR)$NGX_CONF_PREFIX/fastcgi.conf.default'

	test -f '\$(DESTDIR)$NGX_CONF_PREFIX/uwsgi_params' \\
		|| cp conf/uwsgi_params '\$(DESTDIR)$NGX_CONF_PREFIX'
	cp conf/uwsgi_params \\
		'\$(DESTDIR)$NGX_CONF_PREFIX/uwsgi_params.default'

	test -f '\$(DESTDIR)$NGX_CONF_PREFIX/scgi_params' \\
		|| cp conf/scgi_params '\$(DESTDIR)$NGX_CONF_PREFIX'
	cp conf/scgi_params \\
		'\$(DESTDIR)$NGX_CONF_PREFIX/scgi_params.default'

	test -f '\$(DESTDIR)$NGX_CONF_PATH' \\
		|| cp conf/nginx.conf '\$(DESTDIR)$NGX_CONF_PATH'
	cp conf/nginx.conf '\$(DESTDIR)$NGX_CONF_PREFIX/nginx.conf.default'

	test -d '\$(DESTDIR)`dirname "$NGX_PID_PATH"`' \\
		|| mkdir -p '\$(DESTDIR)`dirname "$NGX_PID_PATH"`'

	test -d '\$(DESTDIR)`dirname "$NGX_HTTP_LOG_PATH"`' \\
		|| mkdir -p '\$(DESTDIR)`dirname "$NGX_HTTP_LOG_PATH"`'

	test -d '\$(DESTDIR)$NGX_PREFIX/html' \\
		|| cp -R $NGX_HTML '\$(DESTDIR)$NGX_PREFIX'
END


if test -n "$NGX_ERROR_LOG_PATH"; then
    cat << END                                                >> $NGX_MAKEFILE

	test -d '\$(DESTDIR)`dirname "$NGX_ERROR_LOG_PATH"`' \\
		|| mkdir -p '\$(DESTDIR)`dirname "$NGX_ERROR_LOG_PATH"`'
END

fi


if test -n "$DYNAMIC_MODULES"; then
    cat << END                                                >> $NGX_MAKEFILE

	test -d '\$(DESTDIR)$NGX_MODULES_PATH' \\
		|| mkdir -p '\$(DESTDIR)$NGX_MODULES_PATH'
END

fi


for ngx_module in $DYNAMIC_MODULES
do
    ngx_module=$ngx_module$ngx_modext

    cat << END                                                >> $NGX_MAKEFILE

	test ! -f '\$(DESTDIR)$NGX_MODULES_PATH/$ngx_module' \\
		|| mv '\$(DESTDIR)$NGX_MODULES_PATH/$ngx_module' \\
			'\$(DESTDIR)$NGX_MODULES_PATH/$ngx_module.old'
	cp $NGX_OBJS/$ngx_module '\$(DESTDIR)$NGX_MODULES_PATH/$ngx_module'
END

done


# create Makefile

cat << END >> Makefile

build:
	\$(MAKE) -f $NGX_MAKEFILE

install:
	\$(MAKE) -f $NGX_MAKEFILE install

modules:
	\$(MAKE) -f $NGX_MAKEFILE modules

upgrade:
	$NGX_SBIN_PATH -t

	kill -USR2 \`cat $NGX_PID_PATH\`
	sleep 1
	test -f $NGX_PID_PATH.oldbin

	kill -QUIT \`cat $NGX_PID_PATH.oldbin\`

.PHONY:	build install modules upgrade
END
