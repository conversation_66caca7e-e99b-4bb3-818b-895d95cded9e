
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


LINK="\$(CC)"

MAIN_LINK=
MODULE_LINK="-shared"

ngx_include_opt="-I "
ngx_compile_opt="-c"
ngx_pic_opt="-fPIC"
ngx_objout="-o "
ngx_binout="-o "
ngx_objext="o"
ngx_binext=
ngx_modext=".so"

ngx_long_start=
ngx_long_end=

ngx_regex_dirsep="\/"
ngx_dirsep='/'

ngx_regex_cont=' \\\
	'
ngx_cont=' \
	'
ngx_tab=' \
		'
ngx_spacer=

ngx_long_regex_cont=$ngx_regex_cont
ngx_long_cont=$ngx_cont

. auto/cc/name

if test -n "$CFLAGS"; then

    CC_TEST_FLAGS="$CFLAGS $NGX_CC_OPT"

    case $NGX_CC_NAME in

        ccc)
            # Compaq C V6.5-207

            ngx_include_opt="-I"
        ;;

        sunc)

            MAIN_LINK=
            MODULE_LINK="-G"

            case "$NGX_MACHINE" in

                i86pc)
                    NGX_AUX=" src/os/unix/ngx_sunpro_x86.il"
                ;;

                sun4u | sun4v)
                    NGX_AUX=" src/os/unix/ngx_sunpro_sparc64.il"
                ;;

            esac

            case $CPU in

                amd64)
                    NGX_AUX=" src/os/unix/ngx_sunpro_amd64.il"
                ;;

            esac
        ;;

    esac

else

    case $NGX_CC_NAME in
        gcc)
            # gcc 2.7.2.3, 2.8.1, 2.95.4, egcs-1.1.2
            #     3.0.4, 3.1.1, 3.2.3, 3.3.2, 3.3.3, 3.3.4, 3.4.0, 3.4.2
            #     4.0.0, 4.0.1, 4.1.0

            . auto/cc/gcc
        ;;

        clang)
            # Clang C compiler

            . auto/cc/clang
        ;;

        icc)
            # Intel C++ compiler 7.1, 8.0, 8.1

            . auto/cc/icc
        ;;

        sunc)
            # Sun C 5.7 Patch 117837-04 2005/05/11

            . auto/cc/sunc
        ;;

        ccc)
            # Compaq C V6.5-207

            . auto/cc/ccc
        ;;

        acc)
            # aCC: HP ANSI C++ B3910B A.03.55.02

            . auto/cc/acc
        ;;

        msvc)
            # MSVC++ 6.0 SP2, MSVC++ Toolkit 2003

            . auto/cc/msvc
        ;;

        owc)
            # Open Watcom C 1.0, 1.2

            . auto/cc/owc
        ;;

        bcc)
            # Borland C++ 5.5

            . auto/cc/bcc
        ;;

    esac

    CC_TEST_FLAGS="$CC_TEST_FLAGS $NGX_CC_OPT"

fi

CFLAGS="$CFLAGS $NGX_CC_OPT"
NGX_TEST_LD_OPT="$NGX_LD_OPT"

if [ "$NGX_PLATFORM" != win32 ]; then

    if test -n "$NGX_LD_OPT"; then
        ngx_feature=--with-ld-opt=\"$NGX_LD_OPT\"
        ngx_feature_name=
        ngx_feature_run=no
        ngx_feature_incs=
        ngx_feature_path=
        ngx_feature_libs=
        ngx_feature_test=
        . auto/feature

        if [ $ngx_found = no ]; then
            echo $0: error: the invalid value in --with-ld-opt=\"$NGX_LD_OPT\"
            echo
            exit 1
        fi
    fi


    ngx_feature="-Wl,-E switch"
    ngx_feature_name=
    ngx_feature_run=no
    ngx_feature_incs=
    ngx_feature_path=
    ngx_feature_libs=-Wl,-E
    ngx_feature_test=
    . auto/feature

    if [ $ngx_found = yes ]; then
        MAIN_LINK="-Wl,-E"
    fi


    if [ "$NGX_CC_NAME" = "sunc" ]; then
        echo "checking for gcc builtin atomic operations ... disabled"
    else
        ngx_feature="gcc builtin atomic operations"
        ngx_feature_name=NGX_HAVE_GCC_ATOMIC
        ngx_feature_run=yes
        ngx_feature_incs=
        ngx_feature_path=
        ngx_feature_libs=
        ngx_feature_test="long  n = 0;
                          if (!__sync_bool_compare_and_swap(&n, 0, 1))
                              return 1;
                          if (__sync_fetch_and_add(&n, 1) != 1)
                              return 1;
                          if (n != 2)
                              return 1;
                          __sync_synchronize();"
        . auto/feature
    fi


    if [ "$NGX_CC_NAME" = "ccc" ]; then
        echo "checking for C99 variadic macros ... disabled"
    else
        ngx_feature="C99 variadic macros"
        ngx_feature_name="NGX_HAVE_C99_VARIADIC_MACROS"
        ngx_feature_run=yes
        ngx_feature_incs="#include <stdio.h>
#define var(dummy, ...)  sprintf(__VA_ARGS__)"
        ngx_feature_path=
        ngx_feature_libs=
        ngx_feature_test="char  buf[30]; buf[0] = '0';
                          var(0, buf, \"%d\", 1);
                          if (buf[0] != '1') return 1"
        . auto/feature
    fi


    ngx_feature="gcc variadic macros"
    ngx_feature_name="NGX_HAVE_GCC_VARIADIC_MACROS"
    ngx_feature_run=yes
    ngx_feature_incs="#include <stdio.h>
#define var(dummy, args...)  sprintf(args)"
    ngx_feature_path=
    ngx_feature_libs=
    ngx_feature_test="char  buf[30]; buf[0] = '0';
                      var(0, buf, \"%d\", 1);
                      if (buf[0] != '1') return 1"
    . auto/feature


    ngx_feature="gcc builtin 64 bit byteswap"
    ngx_feature_name="NGX_HAVE_GCC_BSWAP64"
    ngx_feature_run=no
    ngx_feature_incs=
    ngx_feature_path=
    ngx_feature_libs=
    ngx_feature_test="if (__builtin_bswap64(0)) return 1"
    . auto/feature


#    ngx_feature="inline"
#    ngx_feature_name=
#    ngx_feature_run=no
#    ngx_feature_incs="int inline f(void) { return 1 }"
#    ngx_feature_path=
#    ngx_feature_libs=
#    ngx_feature_test=
#    . auto/feature
#
#    if [ $ngx_found = yes ]; then
#    fi

fi
