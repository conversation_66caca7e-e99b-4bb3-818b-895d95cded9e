
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


echo "checking for $NGX_SYSTEM specific features"

case "$NGX_PLATFORM" in

    FreeBSD:*)
        . auto/os/freebsd
    ;;

    Linux:*)
        . auto/os/linux
    ;;

    SunOS:*)
        . auto/os/solaris
    ;;

    Darwin:*)
        . auto/os/darwin
    ;;

    win32)
        . auto/os/win32
    ;;

    DragonFly:*)
        have=NGX_FREEBSD . auto/have_headers
        CORE_INCS="$UNIX_INCS"
        CORE_DEPS="$UNIX_DEPS $FREEBSD_DEPS"
        CORE_SRCS="$UNIX_SRCS $FREEBSD_SRCS"

        echo " + sendfile() found"
        have=NGX_HAVE_SENDFILE . auto/have
        CORE_SRCS="$CORE_SRCS $FREEBSD_SENDFILE_SRCS"

        ngx_spacer='
'
    ;;

    NetBSD:*)
        CORE_INCS="$UNIX_INCS"
        CORE_DEPS="$UNIX_DEPS $POSIX_DEPS"
        CORE_SRCS="$UNIX_SRCS"

        NGX_RPATH=YES
    ;;

    HP-UX:*)
        # HP/UX
        have=NGX_HPUX . auto/have_headers
        CORE_INCS="$UNIX_INCS"
        CORE_DEPS="$UNIX_DEPS $POSIX_DEPS"
        CORE_SRCS="$UNIX_SRCS"
        CC_AUX_FLAGS="$CC_AUX_FLAGS -D_XOPEN_SOURCE -D_XOPEN_SOURCE_EXTENDED=1"
        CC_AUX_FLAGS="$CC_AUX_FLAGS -D_HPUX_ALT_XOPEN_SOCKET_API"
    ;;

    OSF1:*)
        # Tru64 UNIX
        have=NGX_TRU64 . auto/have_headers
        have=NGX_HAVE_STRERROR_R . auto/nohave
        CORE_INCS="$UNIX_INCS"
        CORE_DEPS="$UNIX_DEPS $POSIX_DEPS"
        CORE_SRCS="$UNIX_SRCS"
    ;;

    GNU:*)
        # GNU Hurd
        have=NGX_GNU_HURD . auto/have_headers
        CORE_INCS="$UNIX_INCS"
        CORE_DEPS="$UNIX_DEPS $POSIX_DEPS"
        CORE_SRCS="$UNIX_SRCS"
        CC_AUX_FLAGS="$CC_AUX_FLAGS -D_GNU_SOURCE -D_FILE_OFFSET_BITS=64"
    ;;

    *)
        CORE_INCS="$UNIX_INCS"
        CORE_DEPS="$UNIX_DEPS $POSIX_DEPS"
        CORE_SRCS="$UNIX_SRCS"
    ;;

esac


case "$NGX_MACHINE" in

    i386 | i686 | i86pc)
        have=NGX_HAVE_NONALIGNED . auto/have
        NGX_MACH_CACHE_LINE=32
    ;;

    amd64 | x86_64)
        have=NGX_HAVE_NONALIGNED . auto/have
        NGX_MACH_CACHE_LINE=64
    ;;

    sun4u | sun4v | sparc | sparc64)
        have=NGX_ALIGNMENT value=16 . auto/define
        # TODO
        NGX_MACH_CACHE_LINE=64
    ;;

    ia64 )
        have=NGX_ALIGNMENT value=16 . auto/define
        # TODO
        NGX_MACH_CACHE_LINE=64
    ;;

    aarch64 | arm64)
        have=NGX_ALIGNMENT value=16 . auto/define
        NGX_MACH_CACHE_LINE=64
    ;;

    ppc64* | powerpc64*)
        have=NGX_ALIGNMENT value=16 . auto/define
        NGX_MACH_CACHE_LINE=128
    ;;

    riscv64)
        have=NGX_ALIGNMENT value=16 . auto/define
        NGX_MACH_CACHE_LINE=64
    ;;

    s390x)
        have=NGX_ALIGNMENT value=16 . auto/define
        NGX_MACH_CACHE_LINE=256
    ;;

    *)
        have=NGX_ALIGNMENT value=16 . auto/define
        NGX_MACH_CACHE_LINE=32
    ;;

esac

if test -z "$NGX_CPU_CACHE_LINE"; then
    NGX_CPU_CACHE_LINE=$NGX_MACH_CACHE_LINE
fi

have=NGX_CPU_CACHE_LINE value=$NGX_CPU_CACHE_LINE . auto/define
