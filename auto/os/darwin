
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


have=NGX_DARWIN . auto/have_headers

CORE_INCS="$UNIX_INCS"
CORE_DEPS="$UNIX_DEPS $DARWIN_DEPS"
CORE_SRCS="$UNIX_SRCS $DARWIN_SRCS"



ngx_spacer='
'

MAIN_LINK=
MODULE_LINK="-shared -Wl,-undefined,dynamic_lookup"

CC_AUX_FLAGS="$CC_AUX_FLAGS -D__APPLE_USE_RFC_3542"


# kqueue

echo " + kqueue found"
have=NGX_HAVE_KQUEUE . auto/have
have=NGX_HAVE_CLEAR_EVENT . auto/have
EVENT_MODULES="$EVENT_MODULES $KQUEUE_MODULE"
CORE_SRCS="$CORE_SRCS $KQUEUE_SRCS"
EVENT_FOUND=YES
NGX_KQUEUE_CHECKED=YES

ngx_feature="kqueue's EVFILT_TIMER"
ngx_feature_name="NGX_HAVE_TIMER_EVENT"
ngx_feature_run=yes
ngx_feature_incs="#include <sys/event.h>
                  #include <sys/time.h>"
ngx_feature_path=
ngx_feature_libs=
ngx_feature_test="int      kq;
                  struct kevent    kev;
                  struct timespec  ts;

                  if ((kq = kqueue()) == -1) return 1;

                  kev.ident = 0;
                  kev.filter = EVFILT_TIMER;
                  kev.flags = EV_ADD|EV_ENABLE;
                  kev.fflags = 0;
                  kev.data = 1000;
                  kev.udata = 0;

                  ts.tv_sec = 0;
                  ts.tv_nsec = 0;

                  if (kevent(kq, &kev, 1, &kev, 1, &ts) == -1) return 1;

                  if (kev.flags & EV_ERROR) return 1;"

. auto/feature


ngx_feature="Darwin 64-bit kqueue millisecond timeout bug"
ngx_feature_name=NGX_DARWIN_KEVENT_BUG
ngx_feature_run=bug
ngx_feature_incs="#include <sys/event.h>
                  #include <sys/time.h>"
ngx_feature_path=
ngx_feature_libs=
ngx_feature_test="int  kq;
                  struct kevent    kev;
                  struct timespec  ts;
                  struct timeval   tv, tv0;

                  kq = kqueue();

                  ts.tv_sec = 0;
                  ts.tv_nsec = 999000000;

                  gettimeofday(&tv, 0);
                  kevent(kq, NULL, 0, &kev, 1, &ts);
                  gettimeofday(&tv0, 0);
                  timersub(&tv0, &tv, &tv);

                  if (tv.tv_sec * 1000000 + tv.tv_usec < 900000) return 1;"

. auto/feature


# sendfile()

ngx_feature="sendfile()"
ngx_feature_name="NGX_HAVE_SENDFILE"
ngx_feature_run=yes
ngx_feature_incs="#include <sys/types.h>
                  #include <sys/socket.h>
                  #include <sys/uio.h>
                  #include <sys/errno.h>"
ngx_feature_path=
ngx_feature_libs=
ngx_feature_test="int s = 0, fd = 1;
                  off_t n; off_t off = 0;
                  n = sendfile(s, fd, off, &n, NULL, 0);
                  if (n == -1 && errno == ENOSYS) return 1"
. auto/feature

if [ $ngx_found = yes ]; then
    CORE_SRCS="$CORE_SRCS $DARWIN_SENDFILE_SRCS"
fi


ngx_feature="atomic(3)"
ngx_feature_name=NGX_DARWIN_ATOMIC
ngx_feature_run=no
ngx_feature_incs="#include <libkern/OSAtomic.h>"
ngx_feature_path=
ngx_feature_libs=
ngx_feature_test="int32_t  lock = 0;
                  if (!OSAtomicCompareAndSwap32Barrier(0, 1, &lock)) return 1"
. auto/feature


ngx_feature="TCP_KEEPALIVE"
ngx_feature_name="NGX_HAVE_KEEPALIVE_TUNABLE"
ngx_feature_run=no
ngx_feature_incs="#include <sys/socket.h>
                  #include <netinet/in.h>
                  #include <netinet/tcp.h>"
ngx_feature_path=
ngx_feature_libs=
ngx_feature_test="setsockopt(0, IPPROTO_TCP, TCP_KEEPALIVE, NULL, 0);
                  setsockopt(0, IPPROTO_TCP, TCP_KEEPINTVL, NULL, 0);
                  setsockopt(0, IPPROTO_TCP, TCP_KEEPCNT, NULL, 0)"
. auto/feature

NGX_KEEPALIVE_CHECKED=YES
