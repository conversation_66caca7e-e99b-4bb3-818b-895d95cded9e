
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


if [ $USE_PCRE = YES -o $PCRE != NONE ]; then
    . auto/lib/pcre/conf

else
    if [ $USE_PCRE = DISABLED -a $HTTP = YES -a $HTTP_REWRITE = YES ]; then

cat << END

$0: error: the HTTP rewrite module requires the PCRE library.
You can either disable the module by using --without-http_rewrite_module
option or you have to enable the PCRE support.

END
        exit 1
    fi
fi


if [ $USE_OPENSSL = YES ]; then
    . auto/lib/openssl/conf
fi

if [ $USE_ZLIB = YES ]; then
    . auto/lib/zlib/conf
fi

if [ $USE_LIBXSLT != NO ]; then
    . auto/lib/libxslt/conf
fi

if [ $USE_LIBGD != NO ]; then
    . auto/lib/libgd/conf
fi

if [ $USE_PERL != NO ]; then
    . auto/lib/perl/conf
fi

if [ $USE_GEOIP != NO ]; then
    . auto/lib/geoip/conf
fi

if [ $NGX_GOOGLE_PERFTOOLS = YES ]; then
    . auto/lib/google-perftools/conf
fi

if [ $NGX_LIBATOMIC != NO ]; then
    . auto/lib/libatomic/conf
fi
