
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


if [ $ZLIB != NONE ]; then
    CORE_INCS="$CORE_INCS $ZLIB"

    case "$NGX_CC_NAME" in

        msvc | owc | bcc)
            have=NGX_ZLIB . auto/have
            LINK_DEPS="$LINK_DEPS $ZLIB/zlib.lib"
            CORE_LIBS="$CORE_LIBS $ZLIB/zlib.lib"
        ;;

        icc)
            have=NGX_ZLIB . auto/have
            LINK_DEPS="$LINK_DEPS $ZLIB/libz.a"

            # to allow -ipo optimization we link with the *.o but not library
            CORE_LIBS="$CORE_LIBS $ZLIB/adler32.o"
            CORE_LIBS="$CORE_LIBS $ZLIB/crc32.o"
            CORE_LIBS="$CORE_LIBS $ZLIB/deflate.o"
            CORE_LIBS="$CORE_LIBS $ZLIB/trees.o"
            CORE_LIBS="$CORE_LIBS $ZLIB/zutil.o"
            CORE_LIBS="$CORE_LIBS $ZLIB/compress.o"

            if [ $ZLIB_ASM != NO ]; then
                CORE_LIBS="$CORE_LIBS $ZLIB/match.o"
            fi
        ;;

        *)
            have=NGX_ZLIB . auto/have
            LINK_DEPS="$LINK_DEPS $ZLIB/libz.a"
            CORE_LIBS="$CORE_LIBS $ZLIB/libz.a"
            #CORE_LIBS="$CORE_LIBS -L $ZLIB -lz"
        ;;

    esac

else

    if [ "$NGX_PLATFORM" != win32 ]; then
        ZLIB=NO

        # FreeBSD, Solaris, Linux

        ngx_feature="zlib library"
        ngx_feature_name="NGX_ZLIB"
        ngx_feature_run=no
        ngx_feature_incs="#include <zlib.h>"
        ngx_feature_path=
        ngx_feature_libs="-lz"
        ngx_feature_test="z_stream z; deflate(&z, Z_NO_FLUSH)"
        . auto/feature


        if [ $ngx_found = yes ]; then
            CORE_LIBS="$CORE_LIBS $ngx_feature_libs"
            ZLIB=YES
            ngx_found=no
        fi
    fi

    if [ $ZLIB != YES ]; then
cat << END

$0: error: the HTTP gzip module requires the zlib library.
You can either disable the module by using --without-http_gzip_module
option, or install the zlib library into the system, or build the zlib library
statically from the source with nginx by using --with-zlib=<path> option.

END
        exit 1
    fi

fi
