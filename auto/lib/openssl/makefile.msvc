
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


all:
	cd $(OPENSSL)

	perl Configure $(OPENSSL_TARGET) no-shared no-threads		\
		--prefix="%cd%/openssl" 				\
		--openssldir="%cd%/openssl/ssl" 			\
		$(OPENSSL_OPT)

	if exist ms\do_ms.bat (						\
		ms\do_ms						\
		&& $(MAKE) -f ms\nt.mak					\
		&& $(MAKE) -f ms\nt.mak install				\
	) else (							\
		$(MAKE)							\
		&& $(MAKE) install_sw					\
	)
