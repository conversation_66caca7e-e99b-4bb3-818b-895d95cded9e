
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


CFLAGS =	-O2 -Ob1 -Oi -Gs $(LIBC) $(CPU_OPT)
PCREFLAGS =	-DHAVE_CONFIG_H -DPCRE_STATIC -DPOSIX_MALLOC_THRESHOLD=10 \
		-DSUPPORT_PCRE8 -DHAVE_MEMMOVE


pcre.lib:
	cd $(PCRE)

	cl -nologo -c $(CFLAGS) -I . $(PCREFLAGS) pcre_*.c

	link -lib -out:pcre.lib -verbose:lib pcre_*.obj

pcre.h:
	cd $(PCRE)

	copy /y pcre.h.generic pcre.h
	copy /y config.h.generic config.h
	copy /y pcre_chartables.c.dist pcre_chartables.c
