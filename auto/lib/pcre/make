
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


if [ $PCRE_LIBRARY = PCRE2 ]; then

    # PCRE2

    if [ $NGX_CC_NAME = msvc ]; then

        # With PCRE2, it is not possible to compile all sources.
        # Since list of source files changes between versions, we
        # test files which might not be present.

        ngx_pcre_srcs="pcre2_auto_possess.c \
                       pcre2_chartables.c \
                       pcre2_compile.c \
                       pcre2_config.c \
                       pcre2_context.c \
                       pcre2_dfa_match.c \
                       pcre2_error.c \
                       pcre2_jit_compile.c \
                       pcre2_maketables.c \
                       pcre2_match.c \
                       pcre2_match_data.c \
                       pcre2_newline.c \
                       pcre2_ord2utf.c \
                       pcre2_pattern_info.c \
                       pcre2_string_utils.c \
                       pcre2_study.c \
                       pcre2_substitute.c \
                       pcre2_substring.c \
                       pcre2_tables.c \
                       pcre2_ucd.c \
                       pcre2_valid_utf.c \
                       pcre2_xclass.c"

        ngx_pcre_test="pcre2_chkdint.c \
                       pcre2_compile_class.c \
                       pcre2_convert.c \
                       pcre2_extuni.c \
                       pcre2_find_bracket.c \
                       pcre2_script_run.c \
                       pcre2_serialize.c"

        for ngx_src in $ngx_pcre_test
        do
            if [ -f $PCRE/src/$ngx_src ]; then
                ngx_pcre_srcs="$ngx_pcre_srcs $ngx_src"
            fi
        done

        ngx_pcre_objs=`echo $ngx_pcre_srcs \
            | sed -e "s#\([^ ]*\.\)c#\1$ngx_objext#g"`

        ngx_pcre_srcs=`echo $ngx_pcre_srcs \
            | sed -e "s/  *\([^ ][^ ]*\)/$ngx_regex_cont\1/g"`
        ngx_pcre_objs=`echo $ngx_pcre_objs \
            | sed -e "s/  *\([^ ][^ ]*\)/$ngx_regex_cont\1/g"`

        cat << END                                            >> $NGX_MAKEFILE

PCRE_CFLAGS =	-O2 -Ob1 -Oi -Gs $LIBC $CPU_OPT
PCRE_FLAGS =	-DHAVE_CONFIG_H -DPCRE2_STATIC -DPCRE2_CODE_UNIT_WIDTH=8 \\
		-DHAVE_MEMMOVE

PCRE_SRCS =	 $ngx_pcre_srcs
PCRE_OBJS =	 $ngx_pcre_objs

$PCRE/src/pcre2.h:
	cd $PCRE/src \\
	&& copy /y config.h.generic config.h \\
	&& copy /y pcre2.h.generic pcre2.h \\
	&& copy /y pcre2_chartables.c.dist pcre2_chartables.c

$PCRE/src/pcre2-8.lib:	$PCRE/src/pcre2.h $NGX_MAKEFILE
	cd $PCRE/src \\
	&& cl -nologo -c \$(PCRE_CFLAGS) -I . \$(PCRE_FLAGS) \$(PCRE_SRCS) \\
	&& link -lib -out:pcre2-8.lib -verbose:lib \$(PCRE_OBJS)

END

    else

        cat << END                                            >> $NGX_MAKEFILE

$PCRE/src/pcre2.h:	$PCRE/Makefile

$PCRE/Makefile:	$NGX_MAKEFILE
	cd $PCRE \\
	&& if [ -f Makefile ]; then \$(MAKE) distclean; fi \\
	&& CC="\$(CC)" CFLAGS="$PCRE_OPT" \\
	./configure --disable-shared $PCRE_CONF_OPT

$PCRE/.libs/libpcre2-8.a:	$PCRE/Makefile
	cd $PCRE \\
	&& \$(MAKE) libpcre2-8.la

END

    fi


else

    # PCRE

    case "$NGX_CC_NAME" in

        msvc)
            ngx_makefile=makefile.msvc
            ngx_opt="CPU_OPT=\"$CPU_OPT\" LIBC=$LIBC"
            ngx_pcre="PCRE=\"$PCRE\""
        ;;

        owc)
            ngx_makefile=makefile.owc
            ngx_opt="CPU_OPT=\"$CPU_OPT\""
            ngx_pcre=`echo PCRE=\"$PCRE\" | sed -e "s/\//$ngx_regex_dirsep/g"`
        ;;

        bcc)
            ngx_makefile=makefile.bcc
            ngx_opt="-DCPU_OPT=\"$CPU_OPT\""
            ngx_pcre=`echo \-DPCRE=\"$PCRE\" \
                | sed -e "s/\//$ngx_regex_dirsep/g"`
        ;;

        *)
            ngx_makefile=
        ;;

    esac


    if [ -n "$ngx_makefile" ]; then

        cat << END                                            >> $NGX_MAKEFILE

`echo "$PCRE/pcre.lib:	$PCRE/pcre.h $NGX_MAKEFILE"			\
	| sed -e "s/\//$ngx_regex_dirsep/g"`
	\$(MAKE) -f auto/lib/pcre/$ngx_makefile $ngx_pcre $ngx_opt

`echo "$PCRE/pcre.h:" | sed -e "s/\//$ngx_regex_dirsep/g"`
	\$(MAKE) -f auto/lib/pcre/$ngx_makefile $ngx_pcre pcre.h

END

    else

        cat << END                                            >> $NGX_MAKEFILE

$PCRE/pcre.h:	$PCRE/Makefile

$PCRE/Makefile:	$NGX_MAKEFILE
	cd $PCRE \\
	&& if [ -f Makefile ]; then \$(MAKE) distclean; fi \\
	&& CC="\$(CC)" CFLAGS="$PCRE_OPT" \\
	./configure --disable-shared $PCRE_CONF_OPT

$PCRE/.libs/libpcre.a:	$PCRE/Makefile
	cd $PCRE \\
	&& \$(MAKE) libpcre.la

END

    fi

fi
