---
name: Bug report
about: Create a report to help us improve
title: ""
labels: "bug"
---

### Environment

Include the result of the following commands:
  - `nginx -V`
  - `uname -a`

### Description

Describe the bug in full detail including expected and actual behavior.
Specify conditions that caused it. Provide the relevant part of nginx
configuration and debug log.

- [ ] The bug is reproducible with the latest version of nginx
- [ ] The nginx configuration is minimized to the smallest possible
to reproduce the issue and doesn't contain third-party modules

#### nginx configuration

```
# Your nginx configuration here
```
or share the configuration in [gist](https://gist.github.com/).

#### nginx debug log

It is advised to enable
[debug logging](http://nginx.org/en/docs/debugging_log.html).
```
# Your nginx debug log here
```
or share the debug log in [gist](https://gist.github.com/).
