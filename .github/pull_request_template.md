### Proposed changes

Describe the use case and detail of the change.

If this pull request addresses an issue on GitHub, make sure to reference that
issue using one of the
[supported keywords](https://docs.github.com/en/github/managing-your-work-on-github/linking-a-pull-request-to-an-issue).

Before creating a pull request, make sure to comply with the
[Contributing Guidelines](https://github.com/nginx/nginx/blob/master/CONTRIBUTING.md).
